{"name": "roka", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix && yarn format", "lint:strict": "eslint --max-warnings=0 src", "typecheck": "tsc --noEmit --incremental false", "test:watch": "jest --watch", "test": "jest", "format": "prettier -w .", "format:check": "prettier -c .", "release": "standard-version", "push-release": "git push --follow-tags origin main", "postbuild": "next-sitemap", "prepare": "husky install", "codegen": "graphql-codegen --config codegen.ts"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.1.1", "@heroicons/react": "^1.0.6", "@pqt/abbreviate": "^1.0.2", "@skyra/discord-components-core": "^3.6.1", "@skyra/discord-components-react": "^3.6.1", "@tailwindcss/line-clamp": "^0.3.1", "@vercel/analytics": "^1.0.2", "clsx": "^1.1.1", "graphql": "^16.8.1", "next": "^14.2.2", "react": "^18.0.0", "react-dom": "^18.0.0", "react-icons": "^4.3.1", "tailwind-merge": "^1.2.0", "tailwindcss-filters": "^3.0.0"}, "devDependencies": {"@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^13.2.0", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.2.5", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@svgr/webpack": "^6.2.1", "@tailwindcss/forms": "^0.5.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@types/react": "^17.0.40", "@types/tailwindcss": "^2.2.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "autoprefixer": "^10.4.19", "eslint": "^7.32.0", "eslint-config-next": "^11.1.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^1.1.5", "husky": "^7.0.4", "jest": "^27.5.1", "lint-staged": "^11.2.6", "next-sitemap": "^1.9.12", "postcss": "^8.4.39", "prettier": "^2.6.0", "prettier-plugin-tailwindcss": "^0.1.8", "standard-version": "^9.3.2", "tailwindcss": "^3.4.4", "typescript": "^4.9.5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,}": ["eslint --max-warnings=0", "prettier -w"], "src/**/*.{json,css,scss,md}": ["prettier -w"]}}