{"type": "module", "license": "MPL-2.0", "scripts": {"build": "tsc && esbuild ./src/index.ts --bundle --platform=node --inject:./import-meta-url.js --define:import.meta.url=import_meta_url --define:process.env.NODE_ENV='production' --outfile=./update/index.js && pkg ./update/index.js --public-packages * --debug --output=./update/index.exe -t latest-win --compress GZip", "postbuild": "rimraf ./update/index.js && rimraf ./update.zip && mkdirp ./update/node_modules/.prisma/client && ncp ./node_modules/.prisma/client/query_engine-windows.dll.node ./update/node_modules/.prisma/client/query_engine-windows.dll.node && ncp ./node_modules/.prisma/client/query_engine-windows.dll.node ./dependencies/node_modules/.prisma/client/query_engine-windows.dll.node && ncp ./assets ./dependencies/assets && ncp ./update/index.exe ./dependencies/index.exe && rimraf ./update/node_modules/.prisma/client/database.sqlite && rimraf ./dependencies/assets/kvk.json", "postinstall": "cross-env OPENCV4NODEJS_DISABLE_AUTOBUILD=1 DATABASE_URL='file:./dev.db' patch-package && prisma migrate deploy && build-opencv --version 4.6.0 rebuild", "start": "cross-env DATABASE_URL='file:./dev.db' tsx watch -r dotenv/config ./src/index.ts", "package": "makensis installer.nsi && powershell Compress-Archive ./update/* update.zip && explorer.exe .", "release": "npm run build && npm run package"}, "dependencies": {"@prisma/client": "^5.18.0", "@types/ini": "^4.1.0", "@u4/opencv4nodejs": "6.5.2", "adb-ts": "^5.0.0", "clipboardy": "^4.0.0", "discord.js": "^14.15.3", "ini": "^4.1.1", "mathjs": "^12.4.2", "node-fetch": "^3.3.2", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "rgb-hex": "^4.1.0", "rxjs": "^7.8.1", "tesseract.js": "^5.0.3", "unzip-stream": "^0.3.1", "write-excel-file": "^1.4.27", "yaml": "^2.3.4", "zod": "^3.22.4"}, "devDependencies": {"@types/unzip-stream": "^0.3.4", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "esbuild": "^0.19.8", "mkdirp": "^3.0.1", "ncp": "^2.0.0", "patch-package": "^8.0.0", "pkg": "^5.8.1", "prisma": "^5.18.0", "rimraf": "^5.0.5", "tsx": "^4.5.0", "typescript": "^5.3.2"}, "opencv4nodejs": {"disableAutoBuild": "1"}}